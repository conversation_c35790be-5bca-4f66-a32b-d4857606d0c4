const router = require('express').Router();
const apiRoutes = require('./api');
const express = require('express');
const path = require('path');

const keys = require('../config/keys');
const { apiURL } = keys.app;

const api = `/${apiURL}`;

// api routes
router.use(api, apiRoutes);

// Serve locally uploaded files
const uploadsPath = path.join(__dirname, '../uploads');
console.log(`Setting up static file serving for uploads from: ${uploadsPath}`);
console.log(`Files will be accessible at: ${api}/uploads/[filename]`);

router.use(`${api}/uploads`, express.static(uploadsPath, {
  setHeaders: (res, filePath) => {
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));

router.use(api, (req, res) => res.status(404).json('No API route found'));

module.exports = router;
