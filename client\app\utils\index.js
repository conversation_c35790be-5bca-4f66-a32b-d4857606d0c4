const colors = [
  '#FF6633',
  '#FFB399',
  '#FF33FF',
  '#00B3E6',
  '#E6B333',
  '#3366E6',
  '#999966',
  '#99FF99',
  '#B34D4D',
  '#80B300',
  '#809900',
  '#E6B3B3',
  '#6680B3',
  '#66991A',
  '#FF99E6',
  '#CCFF1A',
  '#FF1A66',
  '#E6331A',
  '#33FFCC',
  '#66994D',
  '#B366CC',
  '#4D8000',
  '#B33300',
  '#CC80CC',
  '#66664D',
  '#991AFF',
  '#E666FF',
  '#4DB3FF',
  '#1AB399',
  '#E666B3',
  '#33991A',
  '#CC9999',
  '#B3B31A',
  '#00E680',
  '#4D8066',
  '#809980',
  '#E6FF80',
  '#1AFF33',
  '#999933',
  '#FF3380',
  '#CCCC00',
  '#66E64D',
  '#4D80CC',
  '#9900B3',
  '#E64D66',
  '#4DB380',
  '#FF4D4D',
  '#99E6E6',
  '#6666FF'
];

export const getRandomColors = () => {
  const index = Math.floor(Math.random() * colors.length);
  return colors[index];
};

let cache = {};
export const getMemoizedRandomColors = s => {
  const color = getRandomColors();

  if (s in cache) {
    return cache[s];
  } else {
    let result = color;
    cache[s] = result;
    return result;
  }
};

// Helper function to handle image URLs
export const getImageUrl = (imageUrl) => {
  if (!imageUrl) {
    return '/images/placeholder-image.png';
  }

  // If the URL is already absolute (starts with http or https), return it as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /api, make it absolute for local development
  if (imageUrl.startsWith('/api/')) {
    // For local development with client on port 8080 and server on port 3000
    if (window.location.hostname === 'localhost' && window.location.port === '8080') {
      return `http://localhost:3000${imageUrl}`;
    }
  }

  // Handle case where imageUrl might be just a filename without path
  // This is a fallback for when the server doesn't properly format the URL
  if (!imageUrl.startsWith('/') && !imageUrl.includes('/')) {
    return `/images/${imageUrl}`;
  }

  // Return the URL as is for other cases
  return imageUrl;
};
