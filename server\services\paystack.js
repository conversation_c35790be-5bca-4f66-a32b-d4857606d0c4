const axios = require('axios');
require('dotenv').config();

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;

const paystack = {
  async initializePayment(amount, email, reference) {
    try {
      const response = await axios.post(
        'https://api.paystack.co/transaction/initialize',
        {
          amount: amount * 100, // convert to kobo
          email,
          currency: 'GHS', // Ghana Cedis
          reference,
          callback_url: `${process.env.CLIENT_URL}/checkout/success`
        },
        {
          headers: {
            Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      throw new Error('Payment initialization failed');
    }
  },

  async verifyPayment(reference) {
    try {
      const response = await axios.get(
        `https://api.paystack.co/transaction/verify/${reference}`,
        {
          headers: {
            Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`
          }
        }
      );
      return response.data;
    } catch (error) {
      throw new Error('Payment verification failed');
    }
  }
};

module.exports = paystack; 