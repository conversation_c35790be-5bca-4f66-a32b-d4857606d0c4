# S3 Bucket Setup Guide for Image Uploads

## Problem
You're getting a 403 Forbidden error when trying to access uploaded images from your S3 bucket `greenfoxbucket9111`. This happens because the bucket doesn't have public read permissions.

## Solution

### Option 1: Automated Setup (Recommended)
Run the setup script from your server directory:

```bash
cd server
node scripts/setup-s3-bucket.js
```

### Option 2: Manual Setup via AWS Console

If the automated script fails due to permissions, follow these steps in the AWS Console:

#### Step 1: Configure Bucket Permissions
1. Go to [AWS S3 Console](https://s3.console.aws.amazon.com/)
2. Find your bucket `greenfoxbucket9111`
3. Click on the bucket name
4. Go to the **Permissions** tab

#### Step 2: Edit Block Public Access Settings
1. In the **Block public access (bucket settings)** section, click **Edit**
2. **Uncheck all four options**:
   - ❌ Block public access to buckets and objects granted through new access control lists (ACLs)
   - ❌ Block public access to buckets and objects granted through any access control lists (ACLs)
   - ❌ Block public access to buckets and objects granted through new public bucket or access point policies
   - ❌ Block public access to buckets and objects granted through any public bucket or access point policies
3. Click **Save changes**
4. Type `confirm` when prompted

#### Step 3: Add Bucket Policy
1. In the **Bucket policy** section, click **Edit**
2. Paste the following policy (replace `greenfoxbucket9111` with your bucket name if different):

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::greenfoxbucket9111/*"
        }
    ]
}
```

3. Click **Save changes**

#### Step 4: Configure CORS (Optional but Recommended)
1. In the **Cross-origin resource sharing (CORS)** section, click **Edit**
2. Paste the following CORS configuration:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": ["ETag"],
        "MaxAgeSeconds": 3000
    }
]
```

3. Click **Save changes**

## Code Changes Made

The following changes have been made to fix the upload functionality:

### 1. Updated `server/utils/storage.js`
- Added `ACL: 'public-read'` to S3 upload parameters
- Improved filename generation to avoid special characters
- Fixed imageKey assignment

### 2. Created Setup Script
- `server/scripts/setup-s3-bucket.js` - Automated bucket configuration

## Testing the Fix

After completing the setup:

1. Restart your server
2. Try uploading a new product with an image
3. The image URL should now be accessible publicly
4. Check the network console - the 403 error should be resolved

## Troubleshooting

### Still getting 403 errors?
1. Double-check that all Block Public Access settings are disabled
2. Verify the bucket policy is correctly applied
3. Make sure your AWS credentials have the necessary permissions
4. Try uploading a new image (existing images might still have old permissions)

### AWS Permissions Issues?
If you can't modify bucket settings, you might need:
- S3 admin permissions on your AWS account
- Or ask your AWS administrator to apply these settings

## Security Note
Making your S3 bucket publicly readable means anyone with the direct URL can access the images. This is normal for e-commerce product images, but be aware of this when uploading sensitive content.
