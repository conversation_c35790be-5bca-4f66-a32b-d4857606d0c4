// Mini cart (White content card)
.mini-cart {
  visibility: hidden;
  position: fixed;
  top: 0;
  right: -100%;
  background-color: $white; // White cart background
  height: 100%;
  width: 470px;
  z-index: 1000;
  box-shadow: -4px 0 16px rgba(20, 21, 23, 0.15); // Woodsmoke shadow
  border-left: 1px solid $border-color-default;
  @include transition($layout-transition-higher-speed);

  @include media-breakpoint-down(xs) {
    width: 88%;
    top: 0;
  }
}

// show hidden cart popup
.mini-cart-open {
  .mini-cart {
    visibility: visible;
    right: 0;
    @include transition($layout-transition-higher-speed);
  }

  .dark-overflow {
    @include dark-overflow();
    @include media-breakpoint-down(xs) {
      width: 100%;
      top: 0;
    }
  }
}

.cart {
  height: 100%;
  display: flex;
  flex-direction: column;

  .cart-body {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 100%;
    background-color: $white;
  }

  .item-box {
    padding: 16px;
    margin-bottom: 8px;
    border-bottom: 1px solid $border-color-default;
    -webkit-transition: all $layout-transition-speed ease-in-out;
    transition: all $layout-transition-speed ease-in-out;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: rgba(221, 221, 209, 0.1); // Subtle Moon Mist hover
    }

    .value {
      color: $woodsmoke; // Woodsmoke for prices
      font-weight: $font-weight-medium;
      font-size: $font-size-large;
    }

    .item-details {
      .item-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: $border-radius-default;
        border: 1px solid $border-color-default;
      }

      .icon-trash {
        color: #ef4444; // Accessible red for delete
        font-size: $font-size-large;
        cursor: pointer;
        -webkit-transition: color $layout-transition-speed ease-in-out;
        transition: color $layout-transition-speed ease-in-out;

        &:hover {
          color: #dc2626; // Darker red on hover
        }
      }

      p {
        margin-bottom: 0;
        color: $font-color;
      }
    }
  }

  .cart-header {
    border-bottom: 1px solid $border-color-default;
    background-color: $white;
    text-align: right;
    padding: 16px 20px;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    @include flex();

    h3 {
      color: $woodsmoke; // Woodsmoke for cart title
      margin: 0;
    }
  }

  .empty-cart {
    height: 100%;
    @include flex();
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .bag-icon {
      width: 50px;
      height: 50px;
    }

    p {
      font-weight: $font-weight-normal;
      margin-top: 12px;
    }
  }

  .cart-checkout {
    background-color: $white;
    border-top: 1px solid $border-color-default;
    padding: 20px;

    .cart-summary {
      padding: 16px;
      background-color: rgba(221, 221, 209, 0.1); // Subtle Moon Mist background
      border-radius: $border-radius-default;
      margin-bottom: 16px;

      p {
        margin-bottom: 0;
        color: $woodsmoke;
      }
    }
  }
}

/* Cart common styles (comprehensive color scheme) */
.summary-item {
  .summary-label {
    color: $font-color; // Accessible secondary text
    font-weight: $font-weight-normal;
    text-transform: capitalize;
  }

  .summary-value {
    color: $woodsmoke; // Woodsmoke for important values
    font-weight: $font-weight-medium;
  }

  &.total {
    .summary-label,
    .summary-value {
      color: $woodsmoke;
      font-weight: $font-weight-bold;
      font-size: $font-size-large;
    }
  }
}
/* end cart common styles */
