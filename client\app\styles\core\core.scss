// Import core styles
@import 'variables';
@import 'mixins';

// layout
@import 'layout';
@import 'utils';

// overrides
@import 'overrides';

// bug fixes
@import 'fixes';

// Right-to-left
@import 'rtl';

// components
@import 'components';
@import 'footer';
@import 'header';
@import 'input';
@import 'button';
@import 'checkbox';
@import 'switch';
@import 'spinner';
@import 'cart';
@import 'newsletter';
@import 'login';
@import 'page404';
@import 'homepage';
@import 'dashboard';
@import 'account';
@import 'users';
@import 'product';
@import 'category';
@import 'brand';
@import 'address';
@import 'subpage';
@import 'table';
@import 'menu';
@import 'shop';
@import 'checkout';
@import 'merchant';
@import 'coming-soon';
@import 'order';
@import 'review';
@import 'wishlist';
@import 'share';
@import 'search';
@import 'radio';
@import 'badge';
@import 'support';
