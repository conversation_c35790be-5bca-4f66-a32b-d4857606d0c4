// header styles (<PERSON> theme)
.header-info {
  padding: 10px 0px;
  background-color: $woodsmoke; // Woodsmoke background

  .fa,
  span {
    color: $white; // White text on dark background
    font-size: $font-size-medium !important;
    font-weight: $font-weight-normal;
    margin: 0px 5px;
  }
}

.header {
  background-color: $woodsmoke; // Woodsmoke header background
  border-bottom: 1px solid rgba(221, 221, 209, 0.2);

  .logo {
    font-size: $font-size-heading-large !important;
    font-weight: $font-weight-normal;
  }
}

.fixed-mobile-header {
  @include media-breakpoint-down(sm) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 998;
  }
}

.top-header {
  // margin-top: 10px;
  // margin-bottom: 10px;
  // height: 95px;
  padding: 16px 0px;

  @include media-breakpoint-up(lg) {
    // height: 60px;

    padding: 22px ​0px;
  }

  .bars-icon {
    &:hover {
      opacity: 0.5;
    }
  }

  .cart-icon {
    position: relative;
    line-height: 100%;

    &:hover {
      opacity: 0.5;
    }
  }

  .fa {
    cursor: pointer;
    color: $theme-dark-gray;
    font-size: $font-size-x-huge !important;
  }

  .col-no-padding {
    @include media-breakpoint-down(sm) {
      padding-right: 15px;
      padding-left: 15px;
    }
  }

  .navbar {
    background-color: $woodsmoke !important; // Woodsmoke navbar background
    justify-content: flex-end;
    padding: 0;

    @include media-breakpoint-down(sm) {
      padding-top: 0;
      padding-bottom: 0.5rem;
      justify-content: center;
    }

    .cart-icon {
      .bag-icon {
        vertical-align: text-bottom;
        color: $white; // White cart icon

        &:hover {
          color: $mantis; // Mantis green on hover
        }
      }
    }
  }

  .navbar-nav {
    flex-direction: row;
    @include media-breakpoint-up(md) {
      margin-left: 20px;
    }

    .nav-link {
      padding-top: 0;
      padding-bottom: 0;
      color: $white; // White navigation links

      &.active {
        color: $mantis !important; // Mantis green for active links
      }
    }

    a,
    span {
      color: $white; // White text

      &:hover {
        color: $mantis !important; // Mantis green on hover
      }
    }

    .dropdown-toggle.nav-link {
      text-transform: capitalize;
    }

    .dropdown-menu {
      &.nav-brand-dropdown {
        padding: 0;
        @include media-breakpoint-down(sm) {
          left: -30px;
          min-width: 270px;
        }
      }
    }

    .nav-item {
      position: relative;
      @include media-breakpoint-down(sm) {
        margin-right: 15px;
      }
    }
  }

  .brand {
    @include flex();
    flex-wrap: wrap;
    align-items: center;
    @include media-breakpoint-down(sm) {
      margin-top: 10px;
      justify-content: center;
    }

    .brand-link {
      text-decoration: none;

      &:hover {
        text-decoration: none;
      }
    }

    .logo-container {
      @include flex();
      align-items: center;
      gap: 12px;
    }

    .logo-image {
      height: 40px;
      width: auto;
      object-fit: contain;

      @include media-breakpoint-up(md) {
        height: 45px;
      }
    }

    .logo-text {
      margin: 0px;
      color: $white !important; // White logo text
      font-size: $font-size-heading-large !important;
      font-weight: $font-weight-medium;

      @include media-breakpoint-up(md) {
        margin-left: 0px;
      }

      &:hover {
        color: $mantis !important; // Mantis green on hover
      }
    }

    .logo {
      margin: 0px;
      color: $white !important; // White fallback logo
      @include media-breakpoint-up(md) {
        margin-left: 15px;
      }
    }
  }

  .cart-badge {
    border-radius: 100%;
    font-size: 0.6rem;
    font-weight: 600;
    position: absolute;
    top: -12px;
    right: -14px;
    text-align: center;
    width: 20px;
    height: 20px;
    background-color: $mantis; // Mantis green cart badge
    color: $white;
    @include flex();
    justify-content: center;
    align-items: center;
  }

  .header-links {
    @include flex();
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

// mini brand
.mini-brand {
  min-width: 270px;
  @include media-breakpoint-up(md) {
    min-width: 500px;
  }

  .min-brand-title {
    border-bottom: $border-primary;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }
}

.mini-brand-list {
  padding: 16px 20px;
  @include media-breakpoint-up(md) {
    padding: 20px 40px;
  }

  .mini-brand-block {
    @include flex();
    flex-wrap: wrap;
    margin-top: 12px;

    .brand-item {
      flex: 100%;
      margin-bottom: 10px;

      @include media-breakpoint-up(md) {
        flex: 50%;
      }

      .brand-link {
        position: relative;
        color: $font-custom-color !important;
        font-size: $font-size-large;
        font-weight: $font-weight-normal;

        &:hover {
          &:after {
            opacity: 1;
            -webkit-transform: translateY(2px);
            -moz-transform: translateY(2px);
            transform: translateY(2px);
          }
        }

        &:after {
          position: absolute;
          top: 100%;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: $primary-bg;
          content: '';
          opacity: 0;
          -webkit-transition: opacity $layout-transition-speed,
            -webkit-transform $layout-transition-speed;
          -moz-transition: opacity $layout-transition-speed,
            -moz-transform $layout-transition-speed;
          transition: opacity $layout-transition-speed,
            transform $layout-transition-speed;
          -webkit-transform: translateY(10px);
          -moz-transform: translateY(10px);
          transform: translateY(10px);
        }
      }
    }
  }
}
