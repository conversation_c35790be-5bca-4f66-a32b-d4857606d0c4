// sass variables

// Green Fox Brand Colors
$woodsmoke: #141517; // Header, footer, product cards (text), section titles, navigation
$mantis: #6bbb57; // Buttons, badges, highlights, active states
$moon-mist: #ddddd1; // Background, subtle borders, footer text
$white: #ffffff; // Cards, text, contrast on dark backgrounds

// Theme colors (updated to Green Fox palette)
$theme-green: $mantis; // Natural, vibrant green accents
$theme-gray: #686867; // Secondary text and elements
$theme-bright-gray: $moon-mist; // Calm and neutral base
$theme-dark-gray: $woodsmoke; // Dark, elegant foundation
$theme-athens-gray: $moon-mist; // Calm and neutral base
$theme-blue: $mantis; // Natural, vibrant green accents
$theme-purple: $mantis; // Natural, vibrant green accents
$theme-white: $white; // Clarity and cleanliness
$theme-light-white: $moon-mist; // Calm and neutral base
$theme-light-blue-white: $moon-mist; // Calm and neutral base

$theme-black: $woodsmoke; // Dark, elegant foundation
$theme-light-black: $woodsmoke; // Dark, elegant foundation
$theme-orange: $mantis; // Natural, vibrant green accents
$theme-yellow: $mantis; // Natural, vibrant green accents
$theme-light-yellow: $moon-mist; // Calm and neutral base
$theme-pink: $white; // Clarity and cleanliness
$theme-light-blue: $mantis; // Natural, vibrant green accents
$theme-bright-red: #ff6b6b; // keeping a distinct red for error states

$primary-color: $mantis; // Natural, vibrant green accents
$primary-bg: $mantis; // Natural, vibrant green accents

$secondary-color: #686867; // gray (secondary text and elements)
$secondary-bg: $moon-mist; // Calm and neutral base

$danger-color: #ff6b6b; // red for error states and alerts
$danger-bg: #ff6b6b; // red for error states and alerts
$danger-hover-bg: #e55c5c; // darker red for hover

$dark-bg: $woodsmoke; // Dark, elegant foundation
$dark-hover-bg: #2a2b2e; // slightly lighter woodsmoke for hover

$default-color: #686867; // gray (secondary text and elements)
$default-bg: #686867; // gray (secondary text and elements)

// standard colors
$blue: #20a8d8;
$indigo: #6610f2 !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #ff5454;
$white: #fff;
$orange: #fabb3d;
$yellow: #ffc107 !default;
$green: #79c447;
$gray: #808081;
$teal: #20c997 !default;
$cyan: #67c2ef;
$black: #000;

$font-family: 'Poppins';
$font-family-heading: 'Poppins';
$font-family-body: 'Poppins';
$font-size-x-small: 12px;
$font-size-small: 13px;
$font-size-medium: 14px;
$font-size-large: 15px;
$font-size-x-large: 16px;
$font-size-xx-large: 18px;
$font-size-huge: 20px;
$font-size-x-huge: 24px;
$font-size-heading-xsmall: 14px;
$font-size-heading-small: 16px;
$font-size-heading-medium: 18px;
$font-size-heading-large: 20px;
$font-size-heading-x-large: 24px;
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// layout variables (updated for Green Fox palette)
$btn-bg: $white; // White for button backgrounds
$btn-bg-hover: $mantis; // Mantis green on hover
$font-color: #686867; // gray (secondary text and elements)
$font-light-color: rgba(20, 21, 23, 0.15); // woodsmoke with opacity
$font-custom-color: $woodsmoke; // Woodsmoke for primary text
$font-subtext-color: $gray; // gray for secondary text
$font-custom-hover-color: $mantis; // Mantis green for hover text
$font-hover-color: $woodsmoke; // Woodsmoke for hover
$font-focus-color: $woodsmoke; // Woodsmoke for focus
$font-heading-color: $woodsmoke; // Woodsmoke for headings
$border-color-default: rgba(221, 221, 209, 0.6); // Moon mist with opacity
$border-color-primary: rgba(221, 221, 209, 0.6); // Moon mist with opacity
$border-hover-color: rgba(107, 187, 87, 0.4); // Mantis with opacity
$border-focus-color: rgba(107, 187, 87, 0.6); // Mantis with opacity
$separator-color: rgba(221, 221, 209, 0.6); // Moon mist with opacity
$dark-overflow-bg: rgba(20, 21, 23, 0.4); // Woodsmoke with opacity
$transparent-bg: rgba(20, 21, 23, 0.12); // Woodsmoke with opacity
$transparent-gray-bg: rgba(221, 221, 209, 0.96); // Moon mist with opacity
$transparent-white-bg: rgba(255, 255, 255, 0.44); // White with opacity
$hover-bg: $moon-mist; // Moon mist for hover backgrounds
$badge: $mantis; // Mantis green for badges
$stars: #686867; // gray for inactive stars
$stars-active: $mantis; // Mantis green for active stars
$disabled-bg: rgba(104, 104, 103, 0.63); // gray with opacity

$outline-color: $mantis; // Mantis green (accent color)
$validation-color: #ff6b6b; // red for validation errors
$outline-box-shadow: 0 0 0 3px rgba(107, 187, 87, 0.4); // Mantis with opacity
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); // medium shadow
$box-shadow-custom: 0 1px 2px 0 rgba(0, 0, 0, 0.1); // small shadow
$box-shadow-primary: 0 2px 4px 0 rgba(0, 0, 0, 0.15); // regular shadow
$box-shadow-secondary: 0 10px 15px -3px rgba(0, 0, 0, 0.1); // large shadow
$border-radius-default: 3px;
$border-radius-primary: 5px;
$border-radius-circle: 50px;
$line-height: 1.5;
$letter-spacing: 0.5px;
$border-default: 1px solid $border-color-default;
$border-primary: 1px solid $border-color-primary;
$layout-max-width: 1200px;
$layout-transition-speed: 0.3s !default;
$layout-transition-higher-speed: 0.6s !default;

// icons
$twitter: url('/images/social-icons/twitter.svg');
$pinterest: url('/images/social-icons/pinterest.svg');
$instagram: url('/images/social-icons/instagram.svg');
$facebook: url('/images/social-icons/facebook.svg');

$bars: url('/images/bars.png');
$chevron-down: url('/images/chevron-down.svg');
$close: url('/images/close.svg');
$bag: url('/images/bag.svg');
