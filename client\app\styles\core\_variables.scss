// sass variables

// Theme colors
$theme-green: #5e6c58; // sage (accent color for buttons, active items)
$theme-gray: #686867; // gray (secondary text and elements)
$theme-bright-gray: #fefcf6; // off-white (secondary backgrounds)
$theme-dark-gray: #686867; // gray (secondary text and elements)
$theme-athens-gray: #fefcf6; // off-white (secondary backgrounds)
$theme-blue: #5e6c58; // sage (accent color for buttons, active items)
$theme-purple: #5e6c58; // sage (accent color for buttons, active items)
$theme-white: #fffcdc; // light pink (light backgrounds and cards)
$theme-light-white: #fefcf6; // off-white (secondary backgrounds)
$theme-light-blue-white: #fefcf6; // off-white (secondary backgrounds)

$theme-black: #14281d; // dark green (main dark background, sidebar)
$theme-light-black: #14281d; // dark green (main dark background, sidebar)
$theme-orange: #5e6c58; // sage (accent color for buttons, active items)
$theme-yellow: #5e6c58; // sage (accent color for buttons, active items)
$theme-light-yellow: #fefcf6; // off-white (secondary backgrounds)
$theme-pink: #fffcdc; // light pink (light backgrounds and cards)
$theme-light-blue: #5e6c58; // sage (accent color for buttons, active items)
$theme-bright-red: #ff6b6b; // keeping a distinct red for error states

$primary-color: #5e6c58; // sage (accent color for buttons, active items)
$primary-bg: #5e6c58; // sage (accent color for buttons, active items)

$secondary-color: #686867; // gray (secondary text and elements)
$secondary-bg: #fefcf6; // off-white (secondary backgrounds)

$danger-color: #ff6b6b; // red for error states and alerts
$danger-bg: #ff6b6b; // red for error states and alerts
$danger-hover-bg: #e55c5c; // darker red for hover

$dark-bg: $theme-black; // dark green (sidebar background)
$dark-hover-bg: #1b3326; // slightly lighter dark green for hover

$default-color: #686867; // gray (secondary text and elements)
$default-bg: #686867; // gray (secondary text and elements)

// standard colors
$blue: #20a8d8;
$indigo: #6610f2 !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #ff5454;
$white: #fff;
$orange: #fabb3d;
$yellow: #ffc107 !default;
$green: #79c447;
$gray: #808081;
$teal: #20c997 !default;
$cyan: #67c2ef;
$black: #000;

$font-family: 'Poppins';
$font-family-heading: 'Poppins';
$font-family-body: 'Poppins';
$font-size-x-small: 12px;
$font-size-small: 13px;
$font-size-medium: 14px;
$font-size-large: 15px;
$font-size-x-large: 16px;
$font-size-xx-large: 18px;
$font-size-huge: 20px;
$font-size-x-huge: 24px;
$font-size-heading-xsmall: 14px;
$font-size-heading-small: 16px;
$font-size-heading-medium: 18px;
$font-size-heading-large: 20px;
$font-size-heading-x-large: 24px;
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// layout variables
$btn-bg: #fffcdc; // light pink for button backgrounds
$btn-bg-hover: $primary-bg; // sage on hover
$font-color: #686867; // gray (secondary text and elements)
$font-light-color: rgba(20, 40, 29, 0.15); // dark green with opacity
$font-custom-color: $theme-black; // dark green for primary text
$font-subtext-color: $gray; // gray for secondary text
$font-custom-hover-color: $theme-blue; // sage for hover text
$font-hover-color: $theme-black; // dark green for hover
$font-focus-color: $theme-black; // dark green for focus
$font-heading-color: #14281d; // dark green for headings
$border-color-default: rgba(94, 108, 88, 0.2); // border color
$border-color-primary: rgba(94, 108, 88, 0.2); // border color
$border-hover-color: rgba(94, 108, 88, 0.4); // border color with more opacity
$border-focus-color: rgba(94, 108, 88, 0.6); // border color with more opacity
$separator-color: rgba(94, 108, 88, 0.2); // border color
$dark-overflow-bg: rgba(20, 40, 29, 0.4); // dark green with opacity
$transparent-bg: rgba(20, 40, 29, 0.12); // dark green with opacity
$transparent-gray-bg: rgba(254, 252, 246, 0.96); // off-white with opacity
$transparent-white-bg: rgba(255, 252, 220, 0.44); // light pink with opacity
$hover-bg: #fefcf6; // off-white (secondary backgrounds)
$badge: $primary-color; // sage for badges
$stars: #686867; // gray for inactive stars
$stars-active: #5e6c58; // sage for active stars
$disabled-bg: rgba(104, 104, 103, 0.63); // gray with opacity

$outline-color: #5e6c58; // sage (accent color)
$validation-color: #ff6b6b; // red for validation errors
$outline-box-shadow: 0 0 0 3px rgba(94, 108, 88, 0.4); // sage with opacity
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); // medium shadow
$box-shadow-custom: 0 1px 2px 0 rgba(0, 0, 0, 0.1); // small shadow
$box-shadow-primary: 0 2px 4px 0 rgba(0, 0, 0, 0.15); // regular shadow
$box-shadow-secondary: 0 10px 15px -3px rgba(0, 0, 0, 0.1); // large shadow
$border-radius-default: 3px;
$border-radius-primary: 5px;
$border-radius-circle: 50px;
$line-height: 1.5;
$letter-spacing: 0.5px;
$border-default: 1px solid $border-color-default;
$border-primary: 1px solid $border-color-primary;
$layout-max-width: 1200px;
$layout-transition-speed: 0.3s !default;
$layout-transition-higher-speed: 0.6s !default;

// icons
$twitter: url('/images/social-icons/twitter.svg');
$pinterest: url('/images/social-icons/pinterest.svg');
$instagram: url('/images/social-icons/instagram.svg');
$facebook: url('/images/social-icons/facebook.svg');

$bars: url('/images/bars.png');
$chevron-down: url('/images/chevron-down.svg');
$close: url('/images/close.svg');
$bag: url('/images/bag.svg');
