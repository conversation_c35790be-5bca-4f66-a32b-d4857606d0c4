// sass variables

// Primary Color Palette
$woodsmoke: #141517; // Headers, footers, dark backgrounds, primary text
$mantis: #6bbb57; // Buttons, accents, highlights, interactive elements
$mantis-dark: #5da84d; // Hover state for Mantis (10% darker)
$moon-mist: #ddddd1; // Page backgrounds, subtle borders, secondary elements
$white: #ffffff; // Content cards, light backgrounds, text on dark areas

// CSS Custom Properties for easy maintenance
:root {
  --woodsmoke: #{$woodsmoke};
  --mantis: #{$mantis};
  --mantis-dark: #{$mantis-dark};
  --moon-mist: #{$moon-mist};
  --white: #{$white};
}

// Theme colors (comprehensive color palette)
$theme-green: $mantis; // Interactive elements and accents
$theme-gray: #6b7280; // Accessible secondary text (improved contrast)
$theme-bright-gray: $moon-mist; // Page backgrounds and subtle elements
$theme-dark-gray: $woodsmoke; // Primary text and dark elements
$theme-athens-gray: $moon-mist; // Neutral backgrounds
$theme-blue: $mantis; // Interactive elements
$theme-purple: $mantis; // Interactive elements
$theme-white: $white; // Content cards and light backgrounds
$theme-light-white: $moon-mist; // Secondary backgrounds
$theme-light-blue-white: $moon-mist; // Subtle backgrounds

$theme-black: $woodsmoke; // Headers, footers, primary text
$theme-light-black: $woodsmoke; // Dark elements
$theme-orange: $mantis; // Interactive elements
$theme-yellow: $mantis; // Interactive elements
$theme-light-yellow: $moon-mist; // Subtle backgrounds
$theme-pink: $white; // Content backgrounds
$theme-light-blue: $mantis; // Interactive elements
$theme-bright-red: #dc2626; // Error states (improved accessibility)

$primary-color: $mantis; // Primary interactive color
$primary-bg: $mantis; // Primary background color

$secondary-color: #6b7280; // Accessible secondary text
$secondary-bg: $moon-mist; // Page backgrounds and subtle elements

$danger-color: #dc2626; // Accessible red for error states
$danger-bg: #dc2626; // Error background
$danger-hover-bg: #b91c1c; // Darker red for hover

$dark-bg: $woodsmoke; // Headers, footers, dark backgrounds
$dark-hover-bg: lighten($woodsmoke, 8%); // Lighter woodsmoke for hover

$default-color: #6b7280; // Accessible secondary text
$default-bg: $moon-mist; // Default neutral background

// standard colors
$blue: #20a8d8;
$indigo: #6610f2 !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #ff5454;
$white: #fff;
$orange: #fabb3d;
$yellow: #ffc107 !default;
$green: #79c447;
$gray: #808081;
$teal: #20c997 !default;
$cyan: #67c2ef;
$black: #000;

$font-family: 'Poppins';
$font-family-heading: 'Poppins';
$font-family-body: 'Poppins';
$font-size-x-small: 12px;
$font-size-small: 13px;
$font-size-medium: 14px;
$font-size-large: 15px;
$font-size-x-large: 16px;
$font-size-xx-large: 18px;
$font-size-huge: 20px;
$font-size-x-huge: 24px;
$font-size-heading-xsmall: 14px;
$font-size-heading-small: 16px;
$font-size-heading-medium: 18px;
$font-size-heading-large: 20px;
$font-size-heading-x-large: 24px;
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Layout variables (comprehensive accessibility-focused palette)
$btn-bg: $white; // White button backgrounds
$btn-bg-hover: $mantis-dark; // Darker mantis on hover
$font-color: #374151; // Accessible body text (improved contrast)
$font-light-color: rgba(20, 21, 23, 0.6); // Woodsmoke with opacity for subtle text
$font-custom-color: $woodsmoke; // Primary text color
$font-subtext-color: #6b7280; // Accessible secondary text
$font-custom-hover-color: $mantis; // Interactive hover color
$font-hover-color: $mantis; // Link hover color
$font-focus-color: $mantis; // Focus state color
$font-heading-color: $woodsmoke; // All headings use Woodsmoke

// Border and separator colors
$border-color-default: rgba(221, 221, 209, 0.8); // Visible borders
$border-color-primary: $mantis; // Primary borders
$border-hover-color: $mantis-dark; // Hover border color
$border-focus-color: $mantis; // Focus border color
$separator-color: rgba(221, 221, 209, 0.8); // Section separators

// Background colors
$dark-overflow-bg: rgba(20, 21, 23, 0.8); // Modal overlays
$transparent-bg: rgba(20, 21, 23, 0.05); // Subtle backgrounds
$transparent-gray-bg: rgba(221, 221, 209, 0.5); // Light overlays
$transparent-white-bg: rgba(255, 255, 255, 0.95); // Semi-transparent white
$hover-bg: rgba(221, 221, 209, 0.3); // Hover backgrounds

// Interactive elements
$badge: $mantis; // Badges and tags
$stars: #d1d5db; // Inactive stars (accessible)
$stars-active: $mantis; // Active stars
$disabled-bg: rgba(107, 114, 128, 0.3); // Disabled elements

// Focus and validation
$outline-color: $mantis; // Focus outline color
$validation-color: #dc2626; // Error validation color
$outline-box-shadow: 0 0 0 3px rgba(107, 187, 87, 0.3); // Focus shadow
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); // medium shadow
$box-shadow-custom: 0 1px 2px 0 rgba(0, 0, 0, 0.1); // small shadow
$box-shadow-primary: 0 2px 4px 0 rgba(0, 0, 0, 0.15); // regular shadow
$box-shadow-secondary: 0 10px 15px -3px rgba(0, 0, 0, 0.1); // large shadow
$border-radius-default: 3px;
$border-radius-primary: 5px;
$border-radius-circle: 50px;
$line-height: 1.5;
$letter-spacing: 0.5px;
$border-default: 1px solid $border-color-default;
$border-primary: 1px solid $border-color-primary;
$layout-max-width: 1200px;
$layout-transition-speed: 0.3s !default;
$layout-transition-higher-speed: 0.6s !default;

// icons
$twitter: url('/images/social-icons/twitter.svg');
$pinterest: url('/images/social-icons/pinterest.svg');
$instagram: url('/images/social-icons/instagram.svg');
$facebook: url('/images/social-icons/facebook.svg');

$bars: url('/images/bars.png');
$chevron-down: url('/images/chevron-down.svg');
$close: url('/images/close.svg');
$bag: url('/images/bag.svg');
