// footer styles (Green Fox theme)
.footer {
  flex-shrink: 0;
  background-color: $woodsmoke; // Woodsmoke footer background
  border-top: 1px solid rgba(221, 221, 209, 0.2);
  color: $moon-mist; // Moon mist text color
}

.footer-content {
  @include flex();
  padding-top: 20px;
  margin: 0 auto;
  @include media-breakpoint-down(sm) {
    flex-direction: column;
  }
}

.footer-block {
  width: 100%;
  margin-right: 16px;
  border-right: 1px solid rgba(221, 221, 209, 0.2);
  padding-top: 20px;

  &:last-child {
    margin-right: 0;
  }

  @include media-breakpoint-down(sm) {
    text-align: center;
    margin: 10px 0px;
    border-right: none;
    border-bottom: 1px solid rgba(221, 221, 209, 0.2);
    padding: 20px 0px;
  }

  &:last-child {
    border-right: 0;
    border-bottom: 0;
  }

  .block-title {
    padding-bottom: 10px;

    h3 {
      color: $white; // White headings
    }
  }

  .footer-link {
    padding-bottom: 4px;

    a {
      color: $moon-mist; // Moon mist links

      &:hover {
        color: $mantis; // Mantis green on hover
        text-decoration: underline !important;
      }
    }
  }
}

// Footer brand styles
.footer-brand-block {
  .footer-brand {
    .footer-brand-link {
      text-decoration: none;

      &:hover {
        text-decoration: none;
      }
    }

    .footer-logo-container {
      @include flex();
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      @include media-breakpoint-down(sm) {
        justify-content: center;
      }
    }

    .footer-logo-image {
      height: 35px;
      width: auto;
      object-fit: contain;
    }

    .footer-logo-text {
      margin: 0;
      color: $white !important; // White logo text
      font-size: $font-size-heading-medium !important;
      font-weight: $font-weight-medium;

      &:hover {
        color: $mantis !important; // Mantis green on hover
      }
    }

    .footer-description {
      color: $moon-mist; // Moon mist description
      font-size: $font-size-medium;
      line-height: 1.5;
      margin-bottom: 0;

      @include media-breakpoint-down(sm) {
        text-align: center;
      }
    }
  }
}

/*
 * Social Icons
 */

.footer-social-item {
  text-align: center;
  span,
  a {
    display: inline-block;
    vertical-align: top;

    &:hover {
      opacity: 0.8;
      transform: scale(1.1);
      transition: all 0.3s ease;
    }
  }

  li {
    padding-bottom: 8px;
    margin: 0 auto;
    display: inline-block;
    @include media-breakpoint-down(sm) {
      margin: 0 4px;
    }

    a {
      margin-left: 10px;
      @include media-breakpoint-down(sm) {
        display: block;
        margin-left: 0px;
      }
    }
  }

  .facebook-icon {
    @include icon($facebook, 40px, 40px);
    background-size: 100%;
  }

  .instagram-icon {
    @include icon($instagram, 40px, 40px);
    background-size: 100%;
    border-radius: 50%;
  }

  .pinterest-icon {
    @include icon($pinterest, 40px, 40px);
    background-size: 100%;
  }

  .twitter-icon {
    @include icon($twitter, 40px, 40px);
    background-size: 100%;
  }
}

/*
 * Copyright
 */

.footer-copyright {
  text-align: center;
  padding: 16px 0px;
  border-top: 1px solid rgba(221, 221, 209, 0.2);
  margin-top: 20px;

  span {
    color: $moon-mist; // Moon mist copyright text
    font-size: $font-size-small;
  }
}
