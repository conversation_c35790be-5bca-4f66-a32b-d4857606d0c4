.panel-body {
  // padding: 10px;

  @include media-breakpoint-down(sm) {
    margin-top: 20px;
  }
}

.panel-sidebar {
  background-color: $white; // White sidebar background
  border-radius: $border-radius-default;
  box-shadow: 0 2px 8px rgba(20, 21, 23, 0.1);

  .panel-title,
  .menu-panel {
    margin: 0;
    padding: 16px 0px;
    text-align: center;
    border: 1px solid $border-color-default;
    border-bottom: none;
    background-color: $white;
  }

  .menu-panel {
    &.collapse {
      border-bottom: 1px solid $border-color-default;

      &:hover {
        background-color: rgba(107, 187, 87, 0.05); // Subtle Mantis hover
      }
    }
  }

  .panel-title {
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .navbar {
    padding: 0px;
  }

  .menu-panel {
    display: none;
    width: 100%;
    @include media-breakpoint-down(sm) {
      display: block;
      cursor: pointer;
    }

    &:hover {
      background-color: transparent;
    }

    .btn-text {
      font-size: $font-size-heading-xsmall !important;
      font-weight: $font-weight-normal;
    }
  }

  .panel-links {
    width: 100%;
    text-align: center;
    margin-bottom: 0;

    li {
      border-top: 1px solid $border-color-default;
      border-right: 1px solid $border-color-default;
      border-left: 1px solid $border-color-default;
      -webkit-transition: all $layout-transition-speed ease-in-out;
      transition: all $layout-transition-speed ease-in-out;

      &:hover {
        background-color: rgba(107, 187, 87, 0.05);

        a {
          color: $mantis !important; // Mantis hover color
        }
      }

      &:last-child {
        border-bottom: 1px solid $border-color-default;
      }

      a {
        display: block;
        padding: 16px 20px;
        text-transform: capitalize;
        color: $woodsmoke !important; // Woodsmoke text
        font-weight: $font-weight-normal;
        -webkit-transition: all $layout-transition-speed ease-in-out;
        transition: all $layout-transition-speed ease-in-out;

        &.active-link {
          font-weight: $font-weight-medium;
          background-color: $mantis; // Mantis active background
          color: $white !important; // White text on active
        }
      }
    }
  }
}
