const express = require('express');
const router = express.Router();
const crypto = require('crypto');

const auth = require('../../middleware/auth');
const paystack = require('../../services/paystack');
const Order = require('../../models/order');

// Initialize payment
router.post('/initialize', auth, async (req, res) => {
  try {
    const { amount, email } = req.body;
    const reference = `ref-${crypto.randomBytes(6).toString('hex')}`;
    
    const response = await paystack.initializePayment(amount, email, reference);
    
    res.status(200).json({
      success: true,
      data: response.data
    });
  } catch (error) {
    res.status(400).json({
      error: 'Payment initialization failed. Please try again.'
    });
  }
});

// Verify payment
router.get('/verify/:reference', auth, async (req, res) => {
  try {
    const { reference } = req.params;
    const response = await paystack.verifyPayment(reference);
    
    if (response.data.status === 'success') {
      // Update order status or handle successful payment
      res.status(200).json({
        success: true,
        data: response.data
      });
    } else {
      res.status(400).json({
        error: 'Payment verification failed'
      });
    }
  } catch (error) {
    res.status(400).json({
      error: 'Payment verification failed. Please try again.'
    });
  }
});

module.exports = router; 