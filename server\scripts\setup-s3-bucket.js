// Load environment variables
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const AWS = require('aws-sdk');
const keys = require('../config/keys');

// Configure AWS
const s3 = new AWS.S3({
  accessKeyId: keys.aws.accessKeyId,
  secretAccessKey: keys.aws.secretAccessKey,
  region: keys.aws.region
});

const bucketName = keys.aws.bucketName;

async function setupS3Bucket() {
  try {
    console.log('AWS Config:', {
      accessKeyId: keys.aws.accessKeyId ? 'Set' : 'Not set',
      secretAccessKey: keys.aws.secretAccessKey ? 'Set' : 'Not set',
      region: keys.aws.region,
      bucketName: keys.aws.bucketName
    });

    if (!bucketName) {
      throw new Error('AWS_BUCKET_NAME environment variable is not set');
    }

    console.log(`Setting up S3 bucket: ${bucketName}`);

    // 1. Set bucket policy to allow public read access
    const bucketPolicy = {
      Version: '2012-10-17',
      Statement: [
        {
          Sid: 'PublicReadGetObject',
          Effect: 'Allow',
          Principal: '*',
          Action: 's3:GetObject',
          Resource: `arn:aws:s3:::${bucketName}/*`
        }
      ]
    };

    const policyParams = {
      Bucket: bucketName,
      Policy: JSON.stringify(bucketPolicy)
    };

    await s3.putBucketPolicy(policyParams).promise();
    console.log('✅ Bucket policy set successfully');

    // 2. Configure CORS for the bucket
    const corsParams = {
      Bucket: bucketName,
      CORSConfiguration: {
        CORSRules: [
          {
            AllowedHeaders: ['*'],
            AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
            AllowedOrigins: ['*'],
            ExposeHeaders: ['ETag'],
            MaxAgeSeconds: 3000
          }
        ]
      }
    };

    await s3.putBucketCors(corsParams).promise();
    console.log('✅ CORS configuration set successfully');

    // 3. Disable block public access settings
    const publicAccessParams = {
      Bucket: bucketName,
      PublicAccessBlockConfiguration: {
        BlockPublicAcls: false,
        IgnorePublicAcls: false,
        BlockPublicPolicy: false,
        RestrictPublicBuckets: false
      }
    };

    await s3.putPublicAccessBlock(publicAccessParams).promise();
    console.log('✅ Public access block settings updated');

    console.log('\n🎉 S3 bucket setup completed successfully!');
    console.log('Your images should now be publicly accessible.');

  } catch (error) {
    console.error('❌ Error setting up S3 bucket:', error.message);

    if (error.code === 'AccessDenied') {
      console.log('\n💡 Possible solutions:');
      console.log('1. Make sure your AWS credentials have the necessary permissions');
      console.log('2. Check if your AWS user has S3 admin permissions');
      console.log('3. You might need to configure these settings manually in the AWS Console');
    }
  }
}

// Run the setup
setupS3Bucket();
