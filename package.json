{"name": "mern-ecommerce", "version": "1.0.0", "description": "MERN Ecommerce", "main": "server/index.js", "scripts": {"dev": "npm-run-all --parallel dev:*", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "postinstall": "npm-run-all --parallel install:*", "install:client": "cd client && npm install", "install:server": "cd server && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/mohamedsamara/mern-ecommerce.git"}, "keywords": ["node", "express", "mongoose", "react", "redux", "redux-thunk", "webpack"], "author": "<PERSON> (https://github.com/mohamed<PERSON>mara)", "license": "MIT", "bugs": {"url": "https://github.com/mohamedsamara/mern-ecommerce/issues"}, "homepage": "https://github.com/mohamedsamara/mern-ecommerce#readme", "devDependencies": {"cross-env": "^5.2.1", "npm-run-all": "^4.1.5"}, "dependencies": {"mongoose": "^8.10.1"}}