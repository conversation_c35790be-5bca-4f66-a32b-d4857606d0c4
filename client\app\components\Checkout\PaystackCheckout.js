import React from 'react';
import PaystackPop from '@paystack/inline-js';
import { useSelector } from 'react-redux';

const PaystackCheckout = ({ amount, onSuccess, onClose }) => {
  const user = useSelector(state => state.account.user);

  const handlePayment = () => {
    const paystack = new PaystackPop();
    paystack.newTransaction({
      key: process.env.PAYSTACK_PUBLIC_KEY,
      email: user.email,
      amount: amount * 100, // convert to pesewas
      currency: 'GHS',
      channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
      label: 'Pay for your order',
      onSuccess: (transaction) => {
        onSuccess(transaction);
      },
      onCancel: () => {
        onClose();
      }
    });
  };

  return (
    <div className="paystack-button-container">
      <button
        className="btn btn-primary"
        onClick={handlePayment}
      >
        Pay with Paystack
      </button>
    </div>
  );
};

export default PaystackCheckout; 