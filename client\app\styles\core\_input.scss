// Input text styles (comprehensive color scheme)
input[type='text'],
input[type='number'],
input[type='password'],
input[type='file'],
input[type='email'],
textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid $border-color-default;
  background-color: $white;
  font-size: $font-size-medium;
  color: $woodsmoke; // Woodsmoke text for readability
  border-radius: $border-radius-default;
  -webkit-transition: all $layout-transition-speed ease-in-out;
  transition: all $layout-transition-speed ease-in-out;
  cursor: text;
  @include appearance();

  &:focus {
    outline: none;
    color: $woodsmoke;
    border-color: $mantis !important; // Mantis focus border
    box-shadow: 0 0 0 3px rgba(107, 187, 87, 0.1); // Subtle Mantis glow
    -webkit-transition: all $layout-transition-speed ease-in-out;
    transition: all $layout-transition-speed ease-in-out;
  }

  &:hover {
    border-color: $mantis;
  }

  &:disabled {
    background-color: $disabled-bg;
    border-color: $border-color-default;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

body.user-is-tabbing textarea {
  box-shadow: none !important;
}
// end input text styles

.input-box,
.select-box {
  .invalid-message {
    visibility: hidden;
    opacity: 0;
    color: $validation-color;
    height: 0;
    @include flex();
    -webkit-transition: $layout-transition-speed;
    transition: $layout-transition-speed;
  }

  &.invalid {
    .input-text,
    textarea {
      border-color: $validation-color;
      -webkit-transition: $layout-transition-speed;
      transition: $layout-transition-speed;

      &:focus {
        border-color: $validation-color !important;
      }
    }

    .invalid-message {
      visibility: visible;
      opacity: 1;
      height: 100%;
      margin-top: 5px;
      -webkit-transition: $layout-transition-speed;
      transition: $layout-transition-speed;
    }
  }
}

.input-box {
  margin: 16px 0px;

  input[type='text'],
  input[type='number'],
  input[type='password'],
  input[type='file'],
  input[type='email'] {
    height: 48px; // Slightly larger for better accessibility
  }

  label {
    margin-bottom: 8px;
    color: $woodsmoke; // Woodsmoke for all labels
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    display: block;
  }
}

.inline-btn-box {
  .input-text-block {
    @include flex();
    flex-direction: row;
  }

  .input-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .input-btn {
    border-left: none !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}

.select-box {
  margin: 16px 0px;

  label {
    margin-bottom: 8px;
    color: $woodsmoke; // Woodsmoke for select labels
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    display: block;
  }

  .select-container {
    .select-option__multi-value__label {
      font-weight: $font-weight-normal;
      font-size: $font-size-small;
      color: $woodsmoke;
    }

    .select-option__control {
      border-color: $border-color-default;
      background-color: $white;

      &:hover {
        border-color: $mantis;
      }

      &.select-option__control--is-focused {
        border-color: $mantis;
        box-shadow: 0 0 0 3px rgba(107, 187, 87, 0.1);
      }
    }
  }
}

@include placeholder {
  font-size: $font-size-small;
  line-height: $line-height;
  text-transform: capitalize;
}
