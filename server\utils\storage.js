const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

const keys = require('../config/keys');

exports.s3Upload = async image => {
  try {
    let imageUrl = '';
    let imageKey = '';

    if (image) {
      // Check if AWS credentials are available
      if (keys.aws.accessKeyId && keys.aws.secretAccessKey) {
        // Use AWS S3 for storage
        const s3bucket = new AWS.S3({
          accessKeyId: keys.aws.accessKeyId,
          secretAccessKey: keys.aws.secretAccessKey,
          region: keys.aws.region
        });

        // Generate a unique filename to avoid conflicts and special characters
        const timestamp = Date.now();
        const fileExtension = path.extname(image.originalname);
        const cleanFilename = image.originalname.replace(/[^a-zA-Z0-9.]/g, '_');
        const uniqueKey = `${timestamp}-${cleanFilename}`;

        const params = {
          Bucket: keys.aws.bucketName,
          Key: uniqueKey,
          Body: image.buffer,
          ContentType: image.mimetype
        };

        const s3Upload = await s3bucket.upload(params).promise();

        imageUrl = s3Upload.Location;
        imageKey = s3Upload.Key;
      } else {
        // Fallback to local file storage
        console.log('Using local file storage for uploads (AWS keys not configured)');

        // Create uploads directory if it doesn't exist
        const uploadsDir = path.join(__dirname, '../uploads');
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // Generate unique filename with timestamp to avoid collisions
        const timestamp = Date.now();
        // Clean the original filename to avoid special characters
        const cleanFilename = image.originalname.replace(/[^a-zA-Z0-9.]/g, '_');
        const filename = `${timestamp}-${cleanFilename}`;
        const filepath = path.join(uploadsDir, filename);

        // Write buffer to file
        fs.writeFileSync(filepath, image.buffer);

        // Set relative URL for the uploaded file
        // Make sure this matches the static file serving route in routes/index.js
        imageUrl = `/api/uploads/${filename}`;
        imageKey = filename;
      }
    }

    return { imageUrl, imageKey };
  } catch (error) {
    console.error('Upload error:', error);
    return { imageUrl: '', imageKey: '' };
  }
};
