/**
 *
 * Checkout
 *
 */

import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import Button from '../../Common/Button';
import PaystackCheckout from '../../Checkout/PaystackCheckout';

const Checkout = props => {
  const { authenticated, handleShopping, handleCheckout, placeOrder, cartTotal } = props;
  const [showPaystack, setShowPaystack] = useState(false);
  const history = useHistory();

  const handlePaymentSuccess = async (response) => {
    // Handle successful payment
    await placeOrder();
    history.push('/order/success');
  };

  const handlePaymentClose = () => {
    setShowPaystack(false);
  };

  const handlePlaceOrder = () => {
    setShowPaystack(true);
  };

  return (
    <div className='easy-checkout'>
      <div className='checkout-actions'>
        <Button
          variant='primary'
          text='Continue shopping'
          onClick={() => handleShopping()}
        />
        {authenticated ? (
          showPaystack ? (
            <PaystackCheckout
              amount={cartTotal}
              onSuccess={handlePaymentSuccess}
              onClose={handlePaymentClose}
            />
          ) : (
            <Button
              variant='primary'
              text='Place Order'
              onClick={handlePlaceOrder}
            />
          )
        ) : (
          <Button
            variant='primary'
            text='Proceed To Checkout'
            onClick={() => handleCheckout()}
          />
        )}
      </div>
    </div>
  );
};

export default Checkout;
