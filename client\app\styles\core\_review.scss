.product-reviews {
  .fa {
    font-size: $font-size-x-large;
  }

  .review-summary {
    .left {
      width: 20%;

      @include media-breakpoint-up(md) {
        width: 25%;
      }
      @include media-breakpoint-up(lg) {
        width: 15%;
      }
    }
    .middle {
      width: 60%;

      @include media-breakpoint-up(md) {
        width: 50%;
      }
      @include media-breakpoint-up(lg) {
        width: 70%;
      }
    }
    .right {
      width: 20%;
      text-align: right;

      @include media-breakpoint-up(md) {
        width: 25%;
      }

      @include media-breakpoint-up(lg) {
        width: 15%;
      }
    }

    .bar-container {
      width: 100%;
      background-color: #f4efe6; // cream (secondary backgrounds)
      text-align: center;
      color: white;
    }
    .bar-5 {
      height: 16px;
      background-color: #5e6c58; // sage (accent color)
    }
    .bar-4 {
      height: 16px;
      background-color: #5e6c58; // sage (accent color) - slightly lighter
      opacity: 0.9;
    }
    .bar-3 {
      height: 16px;
      background-color: #5e6c58; // sage (accent color) - medium
      opacity: 0.8;
    }
    .bar-2 {
      height: 16px;
      background-color: #5e6c58; // sage (accent color) - slightly darker
      opacity: 0.7;
    }
    .bar-1 {
      height: 16px;
      background-color: #5e6c58; // sage (accent color) - darkest
      opacity: 0.6;
    }
  }

  .review-list {
    .review-box {
      height: 100%;
      border-radius: $border-radius-default;
      box-shadow: $box-shadow-secondary;
      background-color: #fffcdc; // light pink (light backgrounds and cards)

      .avatar {
        width: 40px;
        height: 40px;
      }

      .review-icon {
        width: 35px;
        height: 35px;

        @include media-breakpoint-up(lg) {
          width: 50px;
          height: 50px;
        }
      }
    }
  }
}

.review-dashboard {
  .r-list {
    .review-box {
      border-radius: $border-radius-default;
      box-shadow: $box-shadow-secondary;
      background-color: #fffcdc; // light pink (light backgrounds and cards)

      .avatar {
        width: 30px;
        height: 30px;
        font-size: $font-size-x-small;

        @include media-breakpoint-up(md) {
          width: 40px;
          height: 40px;
          font-size: $font-size-medium;
        }
      }

      .review-content {
        width: 100%;

        @include media-breakpoint-up(lg) {
          width: 80%;
        }
      }
    }

    .review-product-box {
      @include media-breakpoint-up(lg) {
        width: 80%;
      }
    }

    .item-image {
      border-radius: $border-radius-default;

      @include media-breakpoint-up(lg) {
        width: 70px;
        height: 70px;
        object-fit: cover;
      }
    }
  }
}
