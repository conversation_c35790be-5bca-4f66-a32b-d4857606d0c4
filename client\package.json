{"name": "client", "version": "1.0.0", "description": "MERN Ecommerce Client", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && cross-env NODE_ENV=production webpack --mode production --config webpack/webpack.prod.js", "dev": "cross-env NODE_ENV=development webpack-dev-server --config webpack/webpack.dev.js"}, "dependencies": {"@fontsource/poppins": "^4.5.9", "@paystack/inline-js": "^2.12.6", "autosuggest-highlight": "^3.1.1", "axios": "^0.21.1", "bootstrap": "^4.3.1", "connected-react-router": "^6.4.0", "dompurify": "^2.3.8", "dotenv": "^8.0.0", "font-awesome": "^4.7.0", "history": "^4.9.0", "mobile-detect": "^1.4.4", "rc-slider": "^9.7.2", "react": "^16.8.6", "react-autosuggest": "^10.1.0", "react-bootstrap-table-next": "^3.1.5", "react-bootstrap-table2-toolkit": "^2.0.1", "react-dom": "^16.8.6", "react-multi-carousel": "^2.5.5", "react-notification-system-redux": "^2.0.0", "react-paginate": "^8.1.3", "react-rating-stars-component": "^2.2.0", "react-redux": "^7.0.3", "react-router-dom": "^5.0.1", "react-select": "^3.0.4", "react-share": "^4.4.0", "reactstrap": "^8.0.0", "redux": "^4.0.1", "redux-thunk": "^2.3.0", "sass": "^1.32.12", "simple-line-icons": "^2.4.1", "socket.io-client": "^4.2.0", "validatorjs": "^3.18.1"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/preset-env": "^7.4.5", "@babel/preset-react": "^7.0.0", "autoprefixer": "^9.6.0", "babel-loader": "^8.0.6", "chalk": "^2.4.2", "copy-webpack-plugin": "^5.0.3", "cross-env": "^5.2.1", "css-loader": "^2.1.1", "cssnano": "^4.1.10", "dotenv-webpack": "^1.8.0", "file-loader": "^4.0.0", "html-webpack-plugin": "^3.2.0", "mini-css-extract-plugin": "^0.7.0", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "rimraf": "^2.6.3", "sass-loader": "^10.2.0", "style-loader": "^0.23.1", "terser-webpack-plugin": "^1.3.0", "webpack": "^4.47.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.2", "webpack-merge": "^4.2.1", "webpack-pwa-manifest": "^4.0.0"}}